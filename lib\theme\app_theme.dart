import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // 定义岱宗AI主题色 - 自然雅致风格
  static const Color deepEmerald = Color(0xFF2D5016);     // 深翠绿 - 主色调
  static const Color inkGreen = Color(0xFF1B4332);        // 墨绿 - 主色调
  static const Color champagneGold = Color(0xFFF7E7CE);   // 香槟金 - 辅助色
  static const Color ivoryWhite = Color(0xFFFFFDD0);      // 象牙白 - 辅助色
  static const Color amberYellow = Color(0xFFFFBF00);     // 琥珀黄 - 强调色

  // 保持兼容性的别名
  static const Color forestGreen = deepEmerald;
  static const Color earthYellow = champagneGold;
  static const Color lightGreen = Color(0xFF4F7942);      // 竹绿 - 中间色调
  static const Color lightYellow = ivoryWhite;

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    fontFamily: GoogleFonts.notoSerif().fontFamily,
    // 设置亮色模式下的背景色为象牙白
    scaffoldBackgroundColor: ivoryWhite,
    colorScheme: ColorScheme.fromSeed(
      seedColor: deepEmerald,
      brightness: Brightness.light,
      primary: deepEmerald,
      secondary: lightGreen,
      tertiary: champagneGold,
      surface: champagneGold.withAlpha(77),
      // 提高亮色模式下的文字对比度
      onSurface: inkGreen, // 使用墨绿色文字，提高对比度和雅致感
      onPrimary: ivoryWhite,
      onSecondary: inkGreen,
      onTertiary: inkGreen,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: deepEmerald,
      foregroundColor: ivoryWhite,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: deepEmerald,
        foregroundColor: ivoryWhite,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
        shadowColor: inkGreen.withAlpha(77),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: champagneGold.withAlpha(102),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: lightGreen.withAlpha(128)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: lightGreen.withAlpha(128)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: deepEmerald, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: TextStyle(color: inkGreen.withAlpha(153)),
    ),
    // 添加下拉菜单主题
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(ivoryWhite),
        elevation: WidgetStateProperty.all(8),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: champagneGold, width: 1),
          ),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
      textStyle: TextStyle(
        inherit: true,
        color: inkGreen,
        fontSize: 16,
      ),
    ),
    // 添加弹出菜单主题
    popupMenuTheme: PopupMenuThemeData(
      color: Colors.white,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        inherit: true,
        color: Color(0xFF333333),
        fontSize: 16,
      ),
    ),
    // 添加页面过渡动画
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    // 添加滚动物理效果
    scrollbarTheme: ScrollbarThemeData(
      thickness: WidgetStateProperty.all(6),
      thumbColor:
          WidgetStateProperty.all(const Color.fromRGBO(58, 107, 53, 0.5)),
      radius: const Radius.circular(3),
      crossAxisMargin: 2,
      mainAxisMargin: 2,
    ),
    // 添加文本主题，确保inherit属性一致
    textTheme: const TextTheme(
      displayLarge: TextStyle(inherit: true),
      displayMedium: TextStyle(inherit: true),
      displaySmall: TextStyle(inherit: true),
      headlineLarge: TextStyle(inherit: true),
      headlineMedium: TextStyle(inherit: true),
      headlineSmall: TextStyle(inherit: true),
      titleLarge: TextStyle(inherit: true),
      titleMedium: TextStyle(inherit: true),
      titleSmall: TextStyle(inherit: true),
      bodyLarge: TextStyle(inherit: true),
      bodyMedium: TextStyle(inherit: true),
      bodySmall: TextStyle(inherit: true),
      labelLarge: TextStyle(inherit: true),
      labelMedium: TextStyle(inherit: true),
      labelSmall: TextStyle(inherit: true),
    ),
    // 添加主要文本主题
    primaryTextTheme: const TextTheme(
      displayLarge: TextStyle(inherit: true),
      displayMedium: TextStyle(inherit: true),
      displaySmall: TextStyle(inherit: true),
      headlineLarge: TextStyle(inherit: true),
      headlineMedium: TextStyle(inherit: true),
      headlineSmall: TextStyle(inherit: true),
      titleLarge: TextStyle(inherit: true),
      titleMedium: TextStyle(inherit: true),
      titleSmall: TextStyle(inherit: true),
      bodyLarge: TextStyle(inherit: true),
      bodyMedium: TextStyle(inherit: true),
      bodySmall: TextStyle(inherit: true),
      labelLarge: TextStyle(inherit: true),
      labelMedium: TextStyle(inherit: true),
      labelSmall: TextStyle(inherit: true),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    fontFamily: GoogleFonts.notoSerif().fontFamily,
    // 设置深色模式下的背景色为纯黑色，增强对比度
    scaffoldBackgroundColor: const Color(0xFF000000),
    colorScheme: ColorScheme.fromSeed(
      seedColor: lightGreen,
      brightness: Brightness.dark,
      primary: lightGreen,
      secondary: champagneGold,
      tertiary: amberYellow,
      // 提高深色模式下的对比度 - 所有文字使用白色
      surface: const Color(0xFF121212), // 深灰色表面
      background: const Color(0xFF000000), // 纯黑背景
      onSurface: Colors.white, // 表面文字使用纯白色
      onBackground: Colors.white, // 背景文字使用纯白色
      onPrimary: Colors.white, // 主色上的文字使用白色
      onSecondary: Colors.black, // 辅助色上的文字使用黑色（因为辅助色是亮色）
      onTertiary: Colors.black, // 第三色上的文字使用黑色
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF121212), // 深灰色AppBar
      foregroundColor: Colors.white, // 白色文字
      elevation: 0,
      iconTheme: IconThemeData(color: Colors.white), // 白色图标
      actionsIconTheme: IconThemeData(color: Colors.white), // 白色操作图标
    ),
    cardTheme: CardTheme(
      elevation: 2,
      color: const Color(0xFF1E1E1E), // 深灰色卡片背景
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    listTileTheme: const ListTileThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      textColor: Colors.white, // 白色文字
      iconColor: Colors.white, // 白色图标
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: lightGreen,
        foregroundColor: Colors.white, // 白色文字
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
        shadowColor: Colors.black.withAlpha(102),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFF2A2A2A), // 深灰色输入框背景
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: lightGreen, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      labelStyle: const TextStyle(color: Colors.white), // 白色标签
      hintStyle: const TextStyle(color: Colors.grey), // 灰色提示文字
    ),
    // 添加下拉菜单主题
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(const Color(0xFF1E1E1E)),
        elevation: WidgetStateProperty.all(8),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
      textStyle: const TextStyle(
        inherit: true,
        color: Colors.white, // 白色文字
        fontSize: 16,
      ),
    ),
    // 添加弹出菜单主题
    popupMenuTheme: PopupMenuThemeData(
      color: const Color(0xFF1E1E1E),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        inherit: true,
        color: Colors.white, // 白色文字
        fontSize: 16,
      ),
    ),
    // 添加对话框主题
    dialogTheme: DialogTheme(
      backgroundColor: const Color(0xFF1E1E1E),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      titleTextStyle: const TextStyle(
        inherit: true,
        color: Colors.white, // 白色标题
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      contentTextStyle: const TextStyle(
        inherit: true,
        color: Colors.white, // 白色内容文字
        fontSize: 16,
      ),
    ),
    // 添加Snackbar主题
    snackBarTheme: SnackBarThemeData(
      backgroundColor: const Color(0xFF1E1E1E),
      contentTextStyle: const TextStyle(
        inherit: true,
        color: Colors.white, // 白色文字
        fontSize: 16,
      ),
      actionTextColor: lightGreen,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 8,
    ),
    // 添加文本按钮主题
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: Colors.white, // 白色文字
        textStyle: const TextStyle(
          inherit: true,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    // 添加页面过渡动画
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    // 添加滚动物理效果
    scrollbarTheme: ScrollbarThemeData(
      thickness: WidgetStateProperty.all(6),
      thumbColor:
          WidgetStateProperty.all(const Color.fromRGBO(147, 184, 132, 0.5)),
      radius: const Radius.circular(3),
      crossAxisMargin: 2,
      mainAxisMargin: 2,
    ),
    // 添加文本主题，确保所有文字都是白色，并保持inherit一致性
    textTheme: const TextTheme(
      displayLarge: TextStyle(inherit: true, color: Colors.white),
      displayMedium: TextStyle(inherit: true, color: Colors.white),
      displaySmall: TextStyle(inherit: true, color: Colors.white),
      headlineLarge: TextStyle(inherit: true, color: Colors.white),
      headlineMedium: TextStyle(inherit: true, color: Colors.white),
      headlineSmall: TextStyle(inherit: true, color: Colors.white),
      titleLarge: TextStyle(inherit: true, color: Colors.white),
      titleMedium: TextStyle(inherit: true, color: Colors.white),
      titleSmall: TextStyle(inherit: true, color: Colors.white),
      bodyLarge: TextStyle(inherit: true, color: Colors.white),
      bodyMedium: TextStyle(inherit: true, color: Colors.white),
      bodySmall: TextStyle(inherit: true, color: Colors.white),
      labelLarge: TextStyle(inherit: true, color: Colors.white),
      labelMedium: TextStyle(inherit: true, color: Colors.white),
      labelSmall: TextStyle(inherit: true, color: Colors.white),
    ),
    // 添加主要文本主题
    primaryTextTheme: const TextTheme(
      displayLarge: TextStyle(inherit: true, color: Colors.white),
      displayMedium: TextStyle(inherit: true, color: Colors.white),
      displaySmall: TextStyle(inherit: true, color: Colors.white),
      headlineLarge: TextStyle(inherit: true, color: Colors.white),
      headlineMedium: TextStyle(inherit: true, color: Colors.white),
      headlineSmall: TextStyle(inherit: true, color: Colors.white),
      titleLarge: TextStyle(inherit: true, color: Colors.white),
      titleMedium: TextStyle(inherit: true, color: Colors.white),
      titleSmall: TextStyle(inherit: true, color: Colors.white),
      bodyLarge: TextStyle(inherit: true, color: Colors.white),
      bodyMedium: TextStyle(inherit: true, color: Colors.white),
      bodySmall: TextStyle(inherit: true, color: Colors.white),
      labelLarge: TextStyle(inherit: true, color: Colors.white),
      labelMedium: TextStyle(inherit: true, color: Colors.white),
      labelSmall: TextStyle(inherit: true, color: Colors.white),
    ),
    // 添加Drawer主题
    drawerTheme: const DrawerThemeData(
      backgroundColor: Color(0xFF121212),
    ),
    // 添加底部导航栏主题
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF121212),
      selectedItemColor: Colors.white,
      unselectedItemColor: Colors.grey,
    ),
    // 添加TabBar主题
    tabBarTheme: const TabBarTheme(
      labelColor: Colors.white,
      unselectedLabelColor: Colors.grey,
      indicatorColor: lightGreen,
    ),
    // 添加Checkbox主题
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return lightGreen;
        }
        return Colors.transparent;
      }),
      checkColor: WidgetStateProperty.all(Colors.white),
    ),
    // 添加Radio主题
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return lightGreen;
        }
        return Colors.grey;
      }),
    ),
    // 添加Switch主题
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return lightGreen;
        }
        return Colors.grey;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return lightGreen.withOpacity(0.5);
        }
        return Colors.grey.withOpacity(0.3);
      }),
    ),
  );
}
