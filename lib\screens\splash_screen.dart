import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;
import 'package:novel_app/theme/app_theme.dart';
import 'package:novel_app/screens/home/<USER>';

/// 岱宗文脉启动动画屏幕
/// 用于展示应用启动动画并处理初始化过程
class SplashScreen extends StatefulWidget {
  final List<String> initializationSteps;

  const SplashScreen({
    Key? key,
    required this.initializationSteps,
  }) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;

  final RxInt _currentStep = 0.obs;
  final RxDouble _progress = 0.0.obs;
  final RxBool _isInitializing = true.obs;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // 淡入动画
    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    // 缩放动画
    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOutBack),
      ),
    );

    // 旋转动画
    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.1, 0.4, curve: Curves.easeInOut),
      ),
    );

    // 启动动画
    _controller.forward();

    // 模拟初始化过程
    _simulateInitialization();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 模拟初始化过程
  void _simulateInitialization() {
    final int totalSteps = widget.initializationSteps.length;
    int currentStep = 0;

    // 使用定时器模拟初始化过程
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (currentStep < totalSteps) {
        _currentStep.value = currentStep;
        _progress.value = (currentStep + 1) / totalSteps;
        currentStep++;
      } else {
        timer.cancel();
        _isInitializing.value = false;

        // 延迟一段时间后导航到主屏幕
        Future.delayed(const Duration(milliseconds: 500), () {
          Get.off(() => const HomeScreen(), transition: Transition.fadeIn);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 动画Logo
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotateAnimation.value,
                  child: FadeTransition(
                    opacity: _fadeInAnimation,
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: _buildLogo(),
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // 应用名称
            FadeTransition(
              opacity: _fadeInAnimation,
              child: Text(
                '岱宗文脉',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onBackground,
                ),
              ),
            ),

            const SizedBox(height: 8),

            // 应用标语
            FadeTransition(
              opacity: _fadeInAnimation,
              child: Text(
                '汲取泰山灵气，承载文脉传承',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onBackground,
                ),
              ),
            ),

            const SizedBox(height: 60),

            // 初始化进度
            SizedBox(
              width: 240,
              child: Column(
                children: [
                  // 进度条
                  Obx(() => LinearProgressIndicator(
                        value: _progress.value,
                        backgroundColor: AppTheme.lightYellow.withOpacity(0.3),
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppTheme.forestGreen),
                        borderRadius: BorderRadius.circular(10),
                        minHeight: 6,
                      )),

                  const SizedBox(height: 16),

                  // 当前初始化步骤
                  Obx(() => _isInitializing.value
                      ? Text(
                          widget.initializationSteps[_currentStep.value],
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.onBackground,
                          ),
                        )
                      : Text(
                          '初始化完成',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.green
                                : AppTheme.forestGreen,
                          ),
                        )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建Logo组件
  Widget _buildLogo() {
    // 根据平台选择不同的Logo样式
    if (!kIsWeb && Platform.isAndroid) {
      return _buildAndroidLogo();
    } else {
      return _buildWindowsLogo();
    }
  }

  /// 构建Android平台Logo
  Widget _buildAndroidLogo() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: AppTheme.forestGreen,
        borderRadius: BorderRadius.circular(60),
        boxShadow: [
          BoxShadow(
            color: AppTheme.forestGreen.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Center(
        child: Icon(
          Icons.auto_stories,
          size: 60,
          color: Colors.white,
        ),
      ),
    );
  }

  /// 构建Windows平台Logo
  Widget _buildWindowsLogo() {
    return Container(
      width: 140,
      height: 140,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.forestGreen.withOpacity(0.2),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: AppTheme.forestGreen,
              borderRadius: BorderRadius.circular(15),
            ),
          ),
          Icon(
            Icons.auto_stories,
            size: 60,
            color: Colors.white,
          ),
        ],
      ),
    );
  }
}
