import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform, File;
import 'package:novel_app/theme/app_theme.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/screens/home/<USER>';
import 'package:novel_app/screens/storage/storage_screen.dart';
import 'package:novel_app/screens/chapter_detail/chapter_detail_screen.dart';
import 'package:novel_app/screens/chapter_edit/chapter_edit_screen.dart';
import 'package:novel_app/screens/character_type/character_type_screen.dart';
import 'package:novel_app/screens/library/library_screen.dart';
import 'package:novel_app/screens/tts/tts_screen.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:novel_app/services/content_review_service.dart';
import 'package:novel_app/services/announcement_service.dart';
import 'package:novel_app/screens/announcement_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:novel_app/services/cache_service.dart';
import 'package:novel_app/controllers/theme_controller.dart';
import 'package:novel_app/controllers/genre_controller.dart';
import 'package:novel_app/controllers/style_controller.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/services/character_type_service.dart';
import 'package:novel_app/adapters/novel_adapter.dart';
import 'package:novel_app/adapters/chapter_adapter.dart';
import 'package:novel_app/controllers/tts_controller.dart';
import 'package:novel_app/screens/tools/tools_screen.dart';

import 'package:novel_app/services/character_generator_service.dart';
import 'package:novel_app/services/background_generator_service.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/screens/import_screen.dart';
import 'package:novel_app/screens/ai_chat/daizong_ai_screen.dart';
import 'package:novel_app/models/text_modification.dart';
import 'package:novel_app/screens/library/novel_folder_view.dart';
import 'package:novel_app/screens/smart_composer_demo_screen.dart';
import 'package:novel_app/screens/novel_edit_with_ai_screen.dart';
import 'package:novel_app/screens/library/quick_ai_setup_screen.dart';
import 'package:novel_app/screens/ai_file_editor_demo_screen.dart';
import 'package:novel_app/controllers/smart_composer_controller.dart';
import 'package:novel_app/screens/chat_history_list_screen.dart';
import 'package:novel_app/screens/update_screen.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/adapters/writing_style_package_adapter.dart';

import 'package:novel_app/services/vector_database_service.dart';
import 'package:novel_app/models/memory_chunk.dart';

// 用户系统相关导入
import 'package:novel_app/models/user.dart';
import 'package:novel_app/models/order.dart';
import 'package:novel_app/models/package.dart';
import 'package:novel_app/services/auth_service.dart';
import 'package:novel_app/services/user_sync_service.dart';
import 'package:novel_app/services/payment_service.dart';
import 'package:novel_app/services/daily_limit_service.dart';
import 'package:novel_app/controllers/user_controller.dart';
import 'package:novel_app/services/hybrid_sync_service.dart';
import 'package:novel_app/langchain/services/novel_generation_service.dart';
import 'package:novel_app/langchain/services/lightweight_generation_service.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/controllers/novel_generation_controller.dart';
import 'package:novel_app/services/ai_outline_parser_service.dart';
import 'package:novel_app/services/enhanced_outline_import_service.dart';
import 'package:novel_app/services/update_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:novel_app/services/embedding_service.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';

import 'package:novel_app/services/novel_chat_service.dart';
import 'package:novel_app/screens/ai_chat/chat_settings_screen.dart';
import 'package:novel_app/services/app_lifecycle_service.dart';
import 'package:novel_app/services/background_service.dart';
import 'package:novel_app/services/cloudbase_sync_service.dart';

import 'package:novel_app/screens/network_test_screen.dart';
import 'package:novel_app/models/chat_session.dart';

import 'package:novel_app/utils/web_config.dart';

/// 清理可能存在typeId冲突的盒子
Future<void> _cleanupConflictingBoxes() async {
  try {
    // 删除可能存在typeId冲突的盒子
    final conflictingBoxes = [
      'smart_composer_settings',
      'smart_composer_sessions',
    ];

    for (final boxName in conflictingBoxes) {
      try {
        if (await Hive.boxExists(boxName)) {
          await Hive.deleteBoxFromDisk(boxName);
          print('已清理冲突的盒子: $boxName');
        }
      } catch (e) {
        print('清理盒子 $boxName 失败: $e');
      }
    }
  } catch (e) {
    print('清理冲突盒子时出错: $e');
  }
}

// 安全地打开Hive盒子的辅助函数
Future<Box> _safeOpenHiveBox(String boxName) async {
  print('尝试打开盒子: $boxName');

  // 检查盒子是否已经打开
  if (Hive.isBoxOpen(boxName)) {
    print('盒子 $boxName 已经打开，直接返回');
    return Hive.box(boxName);
  }

  try {
    print('打开盒子: $boxName');
    final box = await Hive.openBox(boxName);
    print('成功打开盒子: $boxName，类型: ${box.runtimeType}');
    return box;
  } catch (e) {
    print('打开 $boxName 盒子失败: $e');

    // 尝试删除锁文件
    if (!kIsWeb) {
      try {
        final appDocDir = await getApplicationDocumentsDirectory();
        final lockFile = File('${appDocDir.path}/$boxName.lock');
        if (await lockFile.exists()) {
          try {
            await lockFile.delete();
            print('已删除锁文件: ${lockFile.path}');
          } catch (e) {
            print('删除锁文件失败: $e');
          }
        }
      } catch (e) {
        print('处理锁文件失败: $e');
      }

      // 尝试删除盒子
      try {
        await Hive.deleteBoxFromDisk(boxName);
        print('已删除盒子: $boxName');
      } catch (e) {
        print('删除盒子失败: $e');
      }
    }

    // 重新尝试打开
    try {
      print('重新尝试打开盒子: $boxName');
      final box = await Hive.openBox(boxName,
          path:
              kIsWeb ? null : (await getApplicationDocumentsDirectory()).path);
      print('成功重新打开盒子: $boxName');
      return box;
    } catch (e) {
      // 如果仍然失败，使用内存中的盒子
      print('重新打开盒子失败，使用内存盒子: $e');
      final box = await Hive.openBox(boxName, path: '');
      print('成功创建内存盒子: $boxName');
      return box;
    }
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化Web版本配置（如果是Web平台）
  if (kIsWeb) {
    print('🌐 开始初始化Web版本配置...');
    print('🔍 当前URL: ${Uri.base}');
    WebConfig.initialize();
    print('✅ Web版本配置初始化完成');
  }

  // 初始化 Hive
  if (kIsWeb) {
    // Web平台使用特殊初始化方式
    await Hive.initFlutter('novel_app_hive');
  } else {
    // 非Web平台使用标准初始化方式
    await Hive.initFlutter();
  }

  // 注册 Novel 和 Chapter 适配器
  if (!Hive.isAdapterRegistered(0)) {
    Hive.registerAdapter(NovelAdapter());
  }
  if (!Hive.isAdapterRegistered(1)) {
    Hive.registerAdapter(ChapterAdapter());
  }

  // 注册其他适配器
  Hive.registerAdapter(WritingStylePackageAdapter());

  // 注册用户系统相关适配器
  if (!Hive.isAdapterRegistered(20)) {
    Hive.registerAdapter(UserAdapter());
  }
  if (!Hive.isAdapterRegistered(21)) {
    Hive.registerAdapter(MembershipTypeAdapter());
  }
  if (!Hive.isAdapterRegistered(22)) {
    Hive.registerAdapter(UserSettingsAdapter());
  }
  if (!Hive.isAdapterRegistered(23)) {
    Hive.registerAdapter(OrderStatusAdapter());
  }
  if (!Hive.isAdapterRegistered(24)) {
    Hive.registerAdapter(PaymentMethodAdapter());
  }
  if (!Hive.isAdapterRegistered(25)) {
    Hive.registerAdapter(OrderAdapter());
  }
  if (!Hive.isAdapterRegistered(26)) {
    Hive.registerAdapter(MembershipPackageAdapter());
  }
  if (!Hive.isAdapterRegistered(27)) {
    Hive.registerAdapter(MembershipLimitsAdapter());
  }
  if (!Hive.isAdapterRegistered(28)) {
    Hive.registerAdapter(MemberCodeAdapter());
  }

  // 注册聊天会话适配器
  if (!Hive.isAdapterRegistered(6)) {
    Hive.registerAdapter(ChatSessionAdapter());
    print('已注册 ChatSessionAdapter');
  }
  if (!Hive.isAdapterRegistered(7)) {
    Hive.registerAdapter(ChatSessionTypeAdapter());
    print('已注册 ChatSessionTypeAdapter');
  }



  // 注册TextModification相关适配器 (使用不同的typeId避免冲突)
  if (!Hive.isAdapterRegistered(30)) {
    Hive.registerAdapter(ModificationTypeAdapter());
    print('已注册 ModificationTypeAdapter');
  }
  if (!Hive.isAdapterRegistered(31)) {
    Hive.registerAdapter(TextModificationAdapter());
    print('已注册 TextModificationAdapter');
  }
  if (!Hive.isAdapterRegistered(32)) {
    Hive.registerAdapter(ModificationStatusAdapter());
    print('已注册 ModificationStatusAdapter');
  }
  if (!Hive.isAdapterRegistered(33)) {
    Hive.registerAdapter(CreativeEditResponseAdapter());
    print('已注册 CreativeEditResponseAdapter');
  }

  // 打开 Hive 盒子
  try {
    // 初始化Hive（Web平台不需要指定路径）
    if (!kIsWeb) {
      try {
        final appDocDir = await getApplicationDocumentsDirectory();
        Hive.init(appDocDir.path);
        print('Hive路径初始化成功: ${appDocDir.path}');
      } catch (e) {
        print('初始化Hive路径失败: $e');
      }
    }

    // 清理可能存在typeId冲突的盒子
    await _cleanupConflictingBoxes();

    // 使用更安全的方式打开Hive盒子
    print('开始打开Hive盒子...');
    await _safeOpenHiveBox('novels');
    print('novels盒子打开成功');

    await _safeOpenHiveBox('generated_chapters');
    print('generated_chapters盒子打开成功');

    // 确保chat_sessions盒子打开成功
    final chatSessionsBox = await _safeOpenHiveBox('chat_sessions');
    print(
        'chat_sessions盒子打开成功，类型: ${chatSessionsBox.runtimeType}，元素数量: ${chatSessionsBox.length}');

    // 检查盒子是否可以正常使用
    try {
      final testKey = 'test_${DateTime.now().millisecondsSinceEpoch}';
      await chatSessionsBox.put(testKey, {'test': 'value'});
      await chatSessionsBox.delete(testKey);
      print('chat_sessions盒子测试成功，可以正常读写');
    } catch (boxTestError) {
      print('chat_sessions盒子测试失败: $boxTestError');
    }
  } catch (e) {
    print('打开Hive盒子失败: $e');
  }

  // 初始化SharedPreferences（确保最先初始化）
  final prefs = await SharedPreferences.getInstance();
  Get.put(prefs);

  // 先初始化应用生命周期服务，确保其他服务可以找到它
  if (!kIsWeb) {
    Get.put(AppLifecycleService());
    print('已初始化应用生命周期服务');
  }

  // 初始化主题控制器
  Get.put(ThemeController());

  // 初始化服务
  final apiConfig = Get.put(ApiConfigController());
  final aiService = Get.put(AIService(apiConfig));
  final cacheService = Get.put(CacheService(prefs));

  // 初始化角色相关服务
  Get.put(CharacterTypeService(prefs));
  Get.put(CharacterCardService(prefs));

  // 初始化文本转语音控制器
  Get.put(TTSController());

  // 删除对NovelGeneratorService的初始化
  Get.put(ContentReviewService(aiService, apiConfig, cacheService));

  // 初始化聊天历史服务（必须在NovelGenerationService之前初始化）
  final chatHistoryService = Get.put(ChatHistoryService());
  await chatHistoryService.init();



  // 初始化LangChain小说生成服务
  Get.put(NovelGenerationService(apiConfigController: apiConfig));

  // 初始化AI大纲解析服务
  Get.put(AIOutlineParserService());

  // 初始化增强版大纲导入服务
  Get.put(EnhancedOutlineImportService());

  // 初始化嵌入服务
  final embeddingService =
      Get.put(EmbeddingService(apiConfigController: apiConfig));

  // 初始化向量数据库服务
  final vectorDatabaseService = Get.put(VectorDatabaseService());
  await vectorDatabaseService.init();

  // 初始化嵌入模型向量化服务
  final vectorizationService = Get.put(NovelVectorizationService(
    apiConfigController: apiConfig,
    embeddingService: embeddingService,
  ));
  await vectorizationService.init();

  // 初始化精简生成服务（必须在向量化服务之后初始化）
  Get.put(LightweightGenerationService(
    apiConfigController: apiConfig,
    vectorizationService: vectorizationService,
  ));

  // 初始化小说聊天服务
  final novelChatService = Get.put(NovelChatService(
    apiConfigController: apiConfig,
    vectorizationService: vectorizationService,
    chatHistoryService: chatHistoryService,
    novelGenerationService: Get.find<NovelGenerationService>(),
  ));
  await novelChatService.init();

  // 初始化小说生成控制器（必须在NovelGenerationService和NovelChatService之后初始化）
  Get.put(NovelGenerationController());

  // 初始化 Smart Composer 控制器
  Get.put(SmartComposerController());

  // 然后初始化控制器
  Get.put(NovelController());
  Get.put(GenreController());
  Get.put(StyleController());
  Get.put(KnowledgeBaseController());
  Get.put(WritingStylePackageController());

  // 初始化角色生成服务和背景生成服务
  Get.put(CharacterGeneratorService(aiService, Get.find<CharacterCardService>(),
      Get.find<CharacterTypeService>()));

  // 初始化背景生成服务
  Get.put(BackgroundGeneratorService(aiService));

  // 初始化公告服务
  final announcementService = Get.put(AnnouncementService());

  // 初始化用户系统服务
  Get.put(AuthService());

  // 初始化CloudBase数据库同步服务
  Get.put(CloudBaseSyncService.instance);

  // 初始化新的混合同步服务（必须在UserSyncService之前）
  Get.put(HybridSyncService());

  Get.put(UserSyncService());
  print('🔍 Main - 开始初始化PaymentService');
  Get.put(PaymentService());
  print('🔍 Main - PaymentService初始化完成');
  Get.put(DailyLimitService()); // 每日限制服务
  Get.put(UserController());

  // 初始化版本更新服务 - 仅在非Web平台使用
  if (!kIsWeb) {
    // 非Web平台使用原生UpdateService
    Get.put(UpdateService());
  }
  // Web平台不初始化更新服务

  // 初始化后台服务
  try {
    // 初始化后台服务
    if (!kIsWeb && Platform.isAndroid) {
      // 对于Android平台，初始化完整的后台服务
      Get.put(BackgroundService());
      print('已初始化Android后台服务');
    } else if (!kIsWeb) {
      // 对于其他非Web平台，也初始化BackgroundService但不启用原生功能
      Get.put(BackgroundService());
      print('已初始化通用后台服务（无原生功能）');
    }
  } catch (e) {
    print('初始化后台服务失败: $e');
  }

  // 直接检查是否有公告需要显示
  if (announcementService.announcement.value != null) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Get.dialog(
        AnnouncementScreen(
            announcement: announcementService.announcement.value!),
        barrierDismissible: false,
      );
    });
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    return Obx(() => GetMaterialApp(
      title: '岱宗文脉',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeController.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      home: const HomeScreen(), // 直接显示主页面，不强制登录
      initialRoute: '/',
      getPages: [
        GetPage(name: '/', page: () => const HomeScreen()),

        GetPage(name: '/storage', page: () => StorageScreen()),
        GetPage(
            name: '/chapter_detail', page: () => const ChapterDetailScreen()),
        GetPage(name: '/chapter_edit', page: () => const ChapterEditScreen()),
        GetPage(name: '/library', page: () => LibraryScreen()),
        GetPage(name: '/character_type', page: () => CharacterTypeScreen()),
        GetPage(name: '/tools', page: () => const ToolsScreen()),
        GetPage(name: '/tts', page: () => TTSScreen()),
        GetPage(name: '/import', page: () => const ImportScreen()),

        GetPage(
            name: '/chat_history', page: () => const ChatHistoryListScreen()),
        // 更新页面路由仅在非Web平台注册
        if (!kIsWeb) GetPage(name: '/update', page: () => const UpdateScreen()),

        GetPage(
          name: '/chat_settings',
          page: () => ChatSettingsScreen(novelTitle: Get.arguments),
        ),
        GetPage(
          name: '/network_test',
          page: () => const NetworkTestScreen(),
        ),

        GetPage(
          name: '/novel_folder_view',
          page: () => const NovelFolderView(),
        ),

        GetPage(
          name: '/quick_ai_setup',
          page: () => const QuickAISetupScreen(),
        ),
        GetPage(
          name: '/ai_file_editor_demo',
          page: () => const AIFileEditorDemoScreen(),
        ),
      ],
    ));
  }
}
